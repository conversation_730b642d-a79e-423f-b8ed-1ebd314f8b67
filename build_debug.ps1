Write-Host "========================================" -ForegroundColor Green
Write-Host "    بناء APK مع تشخيص الأخطاء" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# تعيين متغيرات البيئة
$env:GRADLE_HOME = "C:\gradle\gradle-7.6.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

Write-Host "تعيين متغيرات البيئة..." -ForegroundColor Yellow
Write-Host "GRADLE_HOME: $env:GRADLE_HOME" -ForegroundColor Cyan
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan

# تنظيف البناء السابق
Write-Host ""
Write-Host "تنظيف البناء السابق..." -ForegroundColor Yellow
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
    Write-Host "✅ تم حذف مجلد dist" -ForegroundColor Green
}

if (Test-Path "android\app\build") {
    Remove-Item -Recurse -Force "android\app\build"
    Write-Host "✅ تم حذف مجلد android build" -ForegroundColor Green
}

# بناء Angular مع تكوين Capacitor
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "بناء Angular مع تكوين Capacitor..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

npm run build:capacitor
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم بناء Angular بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ خطأ في بناء Angular" -ForegroundColor Red
    Write-Host "جاري المحاولة مع البناء العادي..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ فشل البناء تماماً" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج..."
        exit 1
    }
}

# التحقق من ملفات البناء
Write-Host ""
Write-Host "التحقق من ملفات البناء..." -ForegroundColor Yellow

$requiredFiles = @(
    "dist\quran-vid-gen\index.html",
    "dist\quran-vid-gen\main.*.js",
    "dist\quran-vid-gen\polyfills.*.js",
    "dist\quran-vid-gen\runtime.*.js"
)

$allFilesExist = $true
foreach ($pattern in $requiredFiles) {
    $files = Get-ChildItem -Path $pattern -ErrorAction SilentlyContinue
    if ($files.Count -eq 0) {
        Write-Host "❌ ملف مفقود: $pattern" -ForegroundColor Red
        $allFilesExist = $false
    } else {
        Write-Host "✅ موجود: $($files[0].Name)" -ForegroundColor Green
    }
}

if (-not $allFilesExist) {
    Write-Host "❌ بعض الملفات المطلوبة مفقودة" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# مزامنة Capacitor
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "مزامنة Capacitor..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

npx cap sync android
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ تم مزامنة Capacitor بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ خطأ في مزامنة Capacitor" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج..."
    exit 1
}

# بناء APK مع تفعيل التشخيص
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "بناء APK مع تفعيل التشخيص..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

cd android
.\gradlew.bat assembleDebug --info --stacktrace
$buildResult = $LASTEXITCODE
cd ..

if ($buildResult -eq 0) {
    Write-Host "✅ تم بناء APK بنجاح" -ForegroundColor Green
} else {
    Write-Host "❌ خطأ في بناء APK" -ForegroundColor Red
    Write-Host "جاري المحاولة مع Capacitor run..." -ForegroundColor Yellow
    
    npx cap run android --no-open
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ فشل البناء تماماً" -ForegroundColor Red
        Read-Host "اضغط Enter للخروج..."
        exit 1
    }
}

# البحث عن ملف APK ونسخه
Write-Host ""
Write-Host "البحث عن ملف APK..." -ForegroundColor Yellow

$apkPaths = @(
    "android\app\build\outputs\apk\debug\app-debug.apk",
    "android\app\build\outputs\apk\release\app-release.apk"
)

$apkFound = $false
$apkPath = ""

foreach ($path in $apkPaths) {
    if (Test-Path $path) {
        $apkPath = $path
        $apkFound = $true
        Write-Host "✅ تم العثور على APK: $path" -ForegroundColor Green
        break
    }
}

if ($apkFound) {
    # معلومات الملف
    $fileInfo = Get-Item $apkPath
    $fileSize = [math]::Round($fileInfo.Length / 1MB, 2)
    Write-Host "حجم الملف: $fileSize MB" -ForegroundColor Cyan
    
    # إنشاء اسم ملف مع التاريخ والوقت
    $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm"
    $desktopPath = "$env:USERPROFILE\Desktop\QuranVidGen_Debug_$timestamp.apk"
    
    try {
        Copy-Item $apkPath $desktopPath -Force
        Write-Host "✅ تم نسخ APK إلى سطح المكتب" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "تم بناء APK بنجاح!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "اسم الملف: QuranVidGen_Debug_$timestamp.apk" -ForegroundColor White
        Write-Host "حجم الملف: $fileSize MB" -ForegroundColor White
        Write-Host "مسار الملف: $desktopPath" -ForegroundColor White
        Write-Host ""
        Write-Host "تعليمات التشخيص:" -ForegroundColor Yellow
        Write-Host "1. ثبت التطبيق على الجهاز" -ForegroundColor White
        Write-Host "2. اضغط مرتين على الشاشة لعرض معلومات التشخيص" -ForegroundColor White
        Write-Host "3. إذا ظهرت شاشة بيضاء، تحقق من logcat:" -ForegroundColor White
        Write-Host "   adb logcat | findstr QuranVidGen" -ForegroundColor Cyan
        
    } catch {
        Write-Host "❌ خطأ في نسخ الملف: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "مسار APK الأصلي: $apkPath" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "APK file not found" -ForegroundColor Red
    Write-Host "Checking build folders:" -ForegroundColor Yellow
    Get-ChildItem -Path "android\app\build\outputs" -Recurse -Filter "*.apk" -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "  - $($_.FullName)" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "Process completed!" -ForegroundColor Green
Read-Host "Press Enter to exit..."
