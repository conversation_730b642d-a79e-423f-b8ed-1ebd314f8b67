import { Injectable } from '@angular/core';
import { Capacitor } from '@capacitor/core';

@Injectable({
  providedIn: 'root'
})
export class DebugService {
  private logs: string[] = [];

  constructor() {
    this.initializeDebug();
  }

  private initializeDebug(): void {
    // تسجيل معلومات النظام
    this.log('=== تشخيص التطبيق ===');
    this.log(`Platform: ${Capacitor.getPlatform()}`);
    this.log(`Is Native: ${Capacitor.isNativePlatform()}`);
    this.log(`User Agent: ${navigator.userAgent}`);
    this.log(`Screen: ${screen.width}x${screen.height}`);
    this.log(`Window: ${window.innerWidth}x${window.innerHeight}`);
    this.log(`Location: ${window.location.href}`);
    
    // معالجة الأخطاء العامة
    window.addEventListener('error', (event) => {
      this.log(`ERROR: ${event.error?.message || event.message}`);
      this.log(`Stack: ${event.error?.stack || 'No stack trace'}`);
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.log(`PROMISE REJECTION: ${event.reason}`);
    });

    // تسجيل تحميل الموارد
    window.addEventListener('load', () => {
      this.log('Window loaded successfully');
    });

    document.addEventListener('DOMContentLoaded', () => {
      this.log('DOM Content loaded');
    });
  }

  log(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    
    console.log(logMessage);
    this.logs.push(logMessage);
    
    // حفظ في localStorage للمراجعة
    try {
      localStorage.setItem('debug_logs', JSON.stringify(this.logs));
    } catch (e) {
      console.warn('Could not save logs to localStorage');
    }
  }

  getLogs(): string[] {
    return this.logs;
  }

  clearLogs(): void {
    this.logs = [];
    localStorage.removeItem('debug_logs');
  }

  exportLogs(): string {
    return this.logs.join('\n');
  }

  // عرض معلومات التشخيص في واجهة المستخدم
  showDebugInfo(): void {
    const debugInfo = this.getDetailedDebugInfo();

    // إنشاء نافذة تشخيص
    const debugDiv = document.createElement('div');
    debugDiv.style.cssText = `
      position: fixed;
      top: 10px;
      left: 10px;
      right: 10px;
      bottom: 10px;
      background: #f5f5f5;
      border: 2px solid #333;
      border-radius: 8px;
      z-index: 20000;
      overflow-y: auto;
      font-family: monospace;
      font-size: 11px;
      padding: 15px;
    `;

    debugDiv.innerHTML = `
      <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid #ccc;">
        <h3 style="margin: 0; color: #333;">Debug Information</h3>
        <button onclick="this.parentElement.parentElement.remove()" style="background: #f44336; color: white; border: none; border-radius: 3px; padding: 5px 10px; cursor: pointer;">Close</button>
      </div>
      <pre style="margin: 0; white-space: pre-wrap; word-break: break-word; line-height: 1.4;">${debugInfo}</pre>
      <div style="margin-top: 15px; text-align: center; padding-top: 10px; border-top: 1px solid #ccc;">
        <button onclick="navigator.clipboard.writeText(\`${debugInfo.replace(/`/g, '\\`')}\`).then(() => alert('Debug info copied!'))" style="padding: 8px 16px; background: #4caf50; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">Copy Debug Info</button>
        <button onclick="location.reload()" style="padding: 8px 16px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer;">Reload App</button>
      </div>
    `;

    document.body.appendChild(debugDiv);
  }

  private getDetailedDebugInfo(): string {
    const info = [];

    info.push('=== SYSTEM INFO ===');
    info.push(`Platform: ${Capacitor.getPlatform()}`);
    info.push(`Is Native: ${Capacitor.isNativePlatform()}`);
    info.push(`User Agent: ${navigator.userAgent}`);
    info.push(`Screen: ${screen.width}x${screen.height}`);
    info.push(`Window: ${window.innerWidth}x${window.innerHeight}`);
    info.push(`Location: ${window.location.href}`);
    info.push(`Document Ready State: ${document.readyState}`);
    info.push(`Angular Loaded: ${document.body.getAttribute('data-angular-loaded') || 'false'}`);
    info.push(`App Ready: ${document.body.getAttribute('data-app-ready') || 'false'}`);

    info.push('\n=== AVAILABLE GLOBALS ===');
    const globals = ['angular', 'ng', 'Zone', 'Capacitor', 'cordova'];
    globals.forEach(global => {
      info.push(`${global}: ${typeof (window as any)[global]}`);
    });

    info.push('\n=== DOM INFO ===');
    info.push(`app-root children: ${document.querySelector('app-root')?.children.length || 0}`);
    info.push(`Scripts loaded: ${document.scripts.length}`);
    info.push(`Stylesheets loaded: ${document.styleSheets.length}`);

    info.push('\n=== ERROR LOGS ===');
    info.push(this.exportLogs());

    return info.join('\n');
  }
}
