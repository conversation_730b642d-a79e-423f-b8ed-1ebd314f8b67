import { Injectable } from '@angular/core';
import { Capacitor } from '@capacitor/core';

@Injectable({
  providedIn: 'root'
})
export class DebugService {
  private logs: string[] = [];

  constructor() {
    this.initializeDebug();
  }

  private initializeDebug(): void {
    // تسجيل معلومات النظام
    this.log('=== تشخيص التطبيق ===');
    this.log(`Platform: ${Capacitor.getPlatform()}`);
    this.log(`Is Native: ${Capacitor.isNativePlatform()}`);
    this.log(`User Agent: ${navigator.userAgent}`);
    this.log(`Screen: ${screen.width}x${screen.height}`);
    this.log(`Window: ${window.innerWidth}x${window.innerHeight}`);
    this.log(`Location: ${window.location.href}`);
    
    // معالجة الأخطاء العامة
    window.addEventListener('error', (event) => {
      this.log(`ERROR: ${event.error?.message || event.message}`);
      this.log(`Stack: ${event.error?.stack || 'No stack trace'}`);
    });

    window.addEventListener('unhandledrejection', (event) => {
      this.log(`PROMISE REJECTION: ${event.reason}`);
    });

    // تسجيل تحميل الموارد
    window.addEventListener('load', () => {
      this.log('Window loaded successfully');
    });

    document.addEventListener('DOMContentLoaded', () => {
      this.log('DOM Content loaded');
    });
  }

  log(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    
    console.log(logMessage);
    this.logs.push(logMessage);
    
    // حفظ في localStorage للمراجعة
    try {
      localStorage.setItem('debug_logs', JSON.stringify(this.logs));
    } catch (e) {
      console.warn('Could not save logs to localStorage');
    }
  }

  getLogs(): string[] {
    return this.logs;
  }

  clearLogs(): void {
    this.logs = [];
    localStorage.removeItem('debug_logs');
  }

  exportLogs(): string {
    return this.logs.join('\n');
  }

  // عرض معلومات التشخيص في واجهة المستخدم
  showDebugInfo(): void {
    if (Capacitor.isNativePlatform()) {
      // في التطبيق الأصلي، أظهر alert
      alert(`Debug Info:\n${this.exportLogs()}`);
    } else {
      // في المتصفح، أظهر في console
      console.group('Debug Info');
      this.logs.forEach(log => console.log(log));
      console.groupEnd();
    }
  }
}
