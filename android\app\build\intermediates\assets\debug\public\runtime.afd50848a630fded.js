(()=>{"use strict";var e,v={},m={};function r(e){var o=m[e];if(void 0!==o)return o.exports;var t=m[e]={exports:{}};return v[e].call(t.exports,t,t.exports,r),t.exports}r.m=v,e=[],r.O=(o,t,i,u)=>{if(!t){var a=1/0;for(n=0;n<e.length;n++){for(var[t,i,u]=e[n],s=!0,d=0;d<t.length;d++)(!1&u||a>=u)&&Object.keys(r.O).every(b=>r.O[b](t[d]))?t.splice(d--,1):(s=!1,u<a&&(a=u));if(s){e.splice(n--,1);var l=i();void 0!==l&&(o=l)}}return o}u=u||0;for(var n=e.length;n>0&&e[n-1][2]>u;n--)e[n]=e[n-1];e[n]=[t,i,u]},r.d=(e,o)=>{for(var t in o)r.o(o,t)&&!r.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:o[t]})},r.f={},r.e=e=>Promise.all(Object.keys(r.f).reduce((o,t)=>(r.f[t](e,o),o),[])),r.u=e=>e+"."+{336:"4681b67c076f2263",937:"d473dccb712fb08d"}[e]+".js",r.miniCssF=e=>{},r.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),(()=>{var e={},o="QuranVidGen:";r.l=(t,i,u,n)=>{if(e[t])e[t].push(i);else{var a,s;if(void 0!==u)for(var d=document.getElementsByTagName("script"),l=0;l<d.length;l++){var f=d[l];if(f.getAttribute("src")==t||f.getAttribute("data-webpack")==o+u){a=f;break}}a||(s=!0,(a=document.createElement("script")).type="module",a.charset="utf-8",a.timeout=120,r.nc&&a.setAttribute("nonce",r.nc),a.setAttribute("data-webpack",o+u),a.src=r.tu(t)),e[t]=[i];var c=(g,b)=>{a.onerror=a.onload=null,clearTimeout(p);var h=e[t];if(delete e[t],a.parentNode&&a.parentNode.removeChild(a),h&&h.forEach(y=>y(b)),g)return g(b)},p=setTimeout(c.bind(null,void 0,{type:"timeout",target:a}),12e4);a.onerror=c.bind(null,a.onerror),a.onload=c.bind(null,a.onload),s&&document.head.appendChild(a)}}})(),r.r=e=>{typeof Symbol<"u"&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;r.tt=()=>(void 0===e&&(e={createScriptURL:o=>o},typeof trustedTypes<"u"&&trustedTypes.createPolicy&&(e=trustedTypes.createPolicy("angular#bundler",e))),e)})(),r.tu=e=>r.tt().createScriptURL(e),r.p="",(()=>{var e={121:0};r.f.j=(i,u)=>{var n=r.o(e,i)?e[i]:void 0;if(0!==n)if(n)u.push(n[2]);else if(121!=i){var a=new Promise((f,c)=>n=e[i]=[f,c]);u.push(n[2]=a);var s=r.p+r.u(i),d=new Error;r.l(s,f=>{if(r.o(e,i)&&(0!==(n=e[i])&&(e[i]=void 0),n)){var c=f&&("load"===f.type?"missing":f.type),p=f&&f.target&&f.target.src;d.message="Loading chunk "+i+" failed.\n("+c+": "+p+")",d.name="ChunkLoadError",d.type=c,d.request=p,n[1](d)}},"chunk-"+i,i)}else e[i]=0},r.O.j=i=>0===e[i];var o=(i,u)=>{var d,l,[n,a,s]=u,f=0;if(n.some(p=>0!==e[p])){for(d in a)r.o(a,d)&&(r.m[d]=a[d]);if(s)var c=s(r)}for(i&&i(u);f<n.length;f++)r.o(e,l=n[f])&&e[l]&&e[l][0](),e[l]=0;return r.O(c)},t=self.webpackChunkQuranVidGen=self.webpackChunkQuranVidGen||[];t.forEach(o.bind(null,0)),t.push=o.bind(null,t.push.bind(t))})()})();