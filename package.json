{"name": "quran-vid-gen", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "build:capacitor": "ng build --configuration=capacitor", "build:prod": "ng build --configuration=production", "watch": "ng build --watch --configuration development", "test": "ng test", "cap:sync": "npx cap sync", "cap:run:android": "npx cap run android", "cap:build:android": "npm run build:capacitor && npx cap sync && npx cap run android --no-open"}, "private": true, "dependencies": {"@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@capacitor/android": "^6.1.0", "@capacitor/cli": "^6.2.1", "@capacitor/core": "^6.2.1", "@capacitor/dialog": "^6.0.0", "@capacitor/filesystem": "^6.0.0", "@ffmpeg/ffmpeg": "^0.12.10", "@ffmpeg/util": "^0.12.1", "@google/generative-ai": "^0.24.1", "@ionic/angular": "^7.0.0", "@ionic/cli": "^7.2.0", "@supabase/supabase-js": "^2.53.0", "bootstrap": "^5.2.3", "googleapis": "^154.1.0", "ngx-bootstrap": "^11.0.2", "primeicons": "^7.0.0", "primeng": "^16.9.1", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.2.1", "@angular/cli": "~16.2.1", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.1.3"}}