import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { enableProdMode } from '@angular/core';

// تفعيل وضع الإنتاج إذا لم يكن في وضع التطوير
if (!/localhost/.test(window.location.href)) {
  enableProdMode();
}

// إضافة معالجة الأخطاء العامة المحسنة
window.addEventListener('error', (event) => {
  console.error('Global error details:', {
    message: event.message,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
    error: event.error,
    stack: event.error?.stack
  });

  let errorDetails = '';
  if (event.error) {
    errorDetails = `Error: ${event.error.message || 'Unknown error'}\n`;
    errorDetails += `File: ${event.filename || 'Unknown file'}\n`;
    errorDetails += `Line: ${event.lineno || 'Unknown'}\n`;
    if (event.error.stack) {
      errorDetails += `Stack: ${event.error.stack.substring(0, 200)}...`;
    }
  } else {
    errorDetails = `Message: ${event.message}\nFile: ${event.filename}\nLine: ${event.lineno}`;
  }

  showErrorMessage('Global Error Details:\n' + errorDetails);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection details:', {
    reason: event.reason,
    promise: event.promise,
    type: typeof event.reason,
    stack: event.reason?.stack
  });

  let rejectionDetails = '';
  if (event.reason) {
    if (typeof event.reason === 'object') {
      rejectionDetails = `Reason: ${JSON.stringify(event.reason, null, 2)}`;
      if (event.reason.stack) {
        rejectionDetails += `\nStack: ${event.reason.stack}`;
      }
    } else {
      rejectionDetails = `Reason: ${event.reason}`;
    }
  }

  showErrorMessage('Promise Rejection:\n' + rejectionDetails);
});

function showErrorMessage(message: string) {
  // إزالة أي رسائل خطأ سابقة
  const existingError = document.getElementById('error-display');
  if (existingError) {
    existingError.remove();
  }

  const errorDiv = document.createElement('div');
  errorDiv.id = 'error-display';
  errorDiv.style.cssText = `
    position: fixed;
    top: 10px;
    left: 10px;
    right: 10px;
    background: #ffebee;
    border: 2px solid #f44336;
    padding: 15px;
    border-radius: 8px;
    z-index: 10000;
    max-height: 70vh;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
    direction: ltr;
    text-align: left;
  `;

  errorDiv.innerHTML = `
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
      <h3 style="color: #f44336; margin: 0; font-size: 14px;">Application Error</h3>
      <button onclick="this.parentElement.parentElement.remove()" style="background: #f44336; color: white; border: none; border-radius: 3px; padding: 5px 10px; cursor: pointer;">×</button>
    </div>
    <pre style="margin: 0; white-space: pre-wrap; word-break: break-word; background: #fff; padding: 10px; border-radius: 4px; border: 1px solid #ddd;">${message}</pre>
    <div style="margin-top: 10px; text-align: center;">
      <button onclick="location.reload()" style="padding: 8px 16px; background: #2196f3; color: white; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">Reload App</button>
      <button onclick="navigator.clipboard.writeText('${message.replace(/'/g, "\\'")}').then(() => alert('Error copied to clipboard'))" style="padding: 8px 16px; background: #4caf50; color: white; border: none; border-radius: 4px; cursor: pointer;">Copy Error</button>
    </div>
  `;

  document.body.appendChild(errorDiv);

  // إخفاء مؤشر التحميل إذا كان موجوداً
  const loader = document.getElementById('initial-loader');
  if (loader) {
    loader.style.display = 'none';
  }
}

// دالة لبدء التطبيق
function startApp() {
  console.log('Starting Angular app...');
  console.log('Location:', window.location.href);
  console.log('User Agent:', navigator.userAgent);
  console.log('Document ready state:', document.readyState);
  console.log('Available modules:', Object.keys(window));

  try {
    platformBrowserDynamic().bootstrapModule(AppModule)
      .then(ref => {
        console.log('Angular app started successfully');
        console.log('App module reference:', ref);

        // إخفاء مؤشر التحميل
        const loader = document.getElementById('initial-loader');
        if (loader) {
          loader.style.display = 'none';
        }

        // إضافة علامة نجاح
        document.body.setAttribute('data-angular-loaded', 'true');
        console.log('Angular bootstrap completed successfully');
      })
      .catch(err => {
        console.error('Bootstrap error details:', {
          error: err,
          message: err.message,
          stack: err.stack,
          name: err.name,
          cause: err.cause
        });

        let errorMessage = 'Angular Bootstrap Failed:\n';
        errorMessage += `Error: ${err.message || 'Unknown error'}\n`;
        errorMessage += `Type: ${err.name || 'Unknown type'}\n`;
        if (err.stack) {
          errorMessage += `Stack: ${err.stack}`;
        }

        showErrorMessage(errorMessage);
      });
  } catch (syncError: any) {
    console.error('Synchronous error during bootstrap:', syncError);
    showErrorMessage(`Synchronous Bootstrap Error:\n${syncError?.message || 'Unknown error'}\n${syncError?.stack || 'No stack trace'}`);
  }
}

// بدء التطبيق عند تحميل DOM
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', startApp);
} else {
  startApp();
}
