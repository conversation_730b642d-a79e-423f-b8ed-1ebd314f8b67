import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { AppModule } from './app/app.module';
import { enableProdMode } from '@angular/core';

// تفعيل وضع الإنتاج إذا لم يكن في وضع التطوير
if (!/localhost/.test(window.location.href)) {
  enableProdMode();
}

// إضافة معالجة الأخطاء العامة
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
  showErrorMessage('Global Error: ' + (event.error?.message || event.message));
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  showErrorMessage('Promise Rejection: ' + event.reason);
});

function showErrorMessage(message: string) {
  const errorDiv = document.createElement('div');
  errorDiv.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 2px solid red;
    padding: 20px;
    border-radius: 10px;
    z-index: 10000;
    max-width: 80%;
    text-align: center;
    font-family: Arial, sans-serif;
  `;
  errorDiv.innerHTML = `
    <h3 style="color: red; margin: 0 0 10px 0;">خطأ في التطبيق</h3>
    <p style="margin: 0 0 10px 0;">${message}</p>
    <button onclick="location.reload()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">إعادة تحميل</button>
  `;
  document.body.appendChild(errorDiv);
}

// دالة لبدء التطبيق
function startApp() {
  console.log('Starting Angular app...');
  console.log('Location:', window.location.href);
  console.log('User Agent:', navigator.userAgent);

  platformBrowserDynamic().bootstrapModule(AppModule)
    .then(ref => {
      console.log('Angular app started successfully');
      // إخفاء مؤشر التحميل
      const loader = document.getElementById('initial-loader');
      if (loader) {
        loader.style.display = 'none';
      }
    })
    .catch(err => {
      console.error('Bootstrap error:', err);
      showErrorMessage('فشل في تحميل التطبيق: ' + err.message);
    });
}

// بدء التطبيق عند تحميل DOM
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', startApp);
} else {
  startApp();
}
