1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="qurangen.pixelpigeon.com"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="22"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:34:5-67
12-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:34:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:35:5-79
13-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:35:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:36:5-76
14-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:36:22-73
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:37:5-80
15-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:37:22-77
16    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
16-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:38:5-81
16-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:38:22-78
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:39:5-65
17-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:39:22-62
18    <uses-permission android:name="android.permission.RECORD_AUDIO" />
18-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:40:5-71
18-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:40:22-68
19    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
19-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:41:5-80
19-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:41:22-77
20    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
20-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:42:5-76
20-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:42:22-73
21    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
21-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:43:5-75
21-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:43:22-72
22    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
22-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:44:5-75
22-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:44:22-72
23
24    <permission
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\08cabe081476e5dd873c73dbe602e334\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
25        android:name="qurangen.pixelpigeon.com.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
25-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\08cabe081476e5dd873c73dbe602e334\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
26        android:protectionLevel="signature" />
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\08cabe081476e5dd873c73dbe602e334\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
27
28    <uses-permission android:name="qurangen.pixelpigeon.com.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
28-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\08cabe081476e5dd873c73dbe602e334\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
28-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\08cabe081476e5dd873c73dbe602e334\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
29
30    <application
30-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:3:5-31:19
31        android:allowBackup="true"
31-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:4:9-35
32        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\08cabe081476e5dd873c73dbe602e334\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
33        android:debuggable="true"
34        android:extractNativeLibs="true"
35        android:icon="@mipmap/ic_launcher"
35-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:5:9-43
36        android:label="@string/app_name"
36-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:6:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:7:9-54
38        android:supportsRtl="true"
38-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:8:9-35
39        android:theme="@style/AppTheme"
39-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:9:9-40
40        android:usesCleartextTraffic="true" >
40-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:10:9-44
41        <activity
41-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:11:9-22:20
42            android:name="qurangen.pixelpigeon.com.MainActivity"
42-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:13:13-41
43            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode"
43-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:12:13-129
44            android:exported="true"
44-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:17:13-36
45            android:label="@string/title_activity_main"
45-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:14:13-56
46            android:launchMode="singleTask"
46-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:16:13-44
47            android:theme="@style/AppTheme.NoActionBarLaunch" >
47-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:15:13-62
48            <intent-filter>
48-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:18:13-21:29
49                <action android:name="android.intent.action.MAIN" />
49-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:19:17-69
49-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:19:25-66
50
51                <category android:name="android.intent.category.LAUNCHER" />
51-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:20:17-77
51-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:20:27-74
52            </intent-filter>
53        </activity>
54
55        <provider
56            android:name="androidx.core.content.FileProvider"
56-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:25:13-62
57            android:authorities="qurangen.pixelpigeon.com.fileprovider"
57-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:26:13-64
58            android:exported="false"
58-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:27:13-37
59            android:grantUriPermissions="true" >
59-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:28:13-47
60            <meta-data
60-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:29:13-112
61                android:name="android.support.FILE_PROVIDER_PATHS"
61-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:29:24-74
62                android:resource="@xml/file_paths" />
62-->C:\Users\<USER>\Desktop\تطبيق ناشر ايات قئانية\android\app\src\main\AndroidManifest.xml:29:75-109
63        </provider>
64        <provider
64-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aaa24fbe223785e25a062b0be8f8e72\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
65            android:name="androidx.startup.InitializationProvider"
65-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aaa24fbe223785e25a062b0be8f8e72\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
66            android:authorities="qurangen.pixelpigeon.com.androidx-startup"
66-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aaa24fbe223785e25a062b0be8f8e72\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
67            android:exported="false" >
67-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aaa24fbe223785e25a062b0be8f8e72\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
68            <meta-data
68-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aaa24fbe223785e25a062b0be8f8e72\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.emoji2.text.EmojiCompatInitializer"
69-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aaa24fbe223785e25a062b0be8f8e72\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
70                android:value="androidx.startup" />
70-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4aaa24fbe223785e25a062b0be8f8e72\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\57dcb267ba1319271c4640dec58c3269\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
72-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\57dcb267ba1319271c4640dec58c3269\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
73                android:value="androidx.startup" />
73-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\57dcb267ba1319271c4640dec58c3269\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
76                android:value="androidx.startup" />
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
77        </provider>
78
79        <receiver
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
80            android:name="androidx.profileinstaller.ProfileInstallReceiver"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
81            android:directBootAware="false"
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
82            android:enabled="true"
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
83            android:exported="true"
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
84            android:permission="android.permission.DUMP" >
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
86                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
89                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
92                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
95                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\800336736568d87e779dcd5e611a9f93\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
96            </intent-filter>
97        </receiver>
98    </application>
99
100</manifest>
