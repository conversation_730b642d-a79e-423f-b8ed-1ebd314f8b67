{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"QuranVidGen": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/quran-vid-gen", "index": "src/index.html", "main": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss", "node_modules/primeng/resources/themes/lara-light-teal/theme.css", "node_modules/primeng/resources/primeng.min.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "50kb", "maximumError": "100kb"}], "outputHashing": "all", "optimization": true, "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "baseHref": "./"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "baseHref": "./"}, "capacitor": {"buildOptimizer": true, "optimization": true, "vendorChunk": false, "extractLicenses": true, "sourceMap": false, "namedChunks": false, "outputHashing": "all", "baseHref": "./"}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "QuranVidGen:build:production"}, "development": {"browserTarget": "QuranVidGen:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "QuranVidGen:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}}}}, "cli": {"analytics": "1e946aa4-b0d8-4bd2-8b07-ebe67838da49"}}