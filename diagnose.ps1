Write-Host "========================================" -ForegroundColor Green
Write-Host "    تشخيص مشكلة الشاشة البيضاء" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# التحقق من ملفات البناء
Write-Host ""
Write-Host "1. التحقق من ملفات البناء..." -ForegroundColor Yellow

if (Test-Path "dist\quran-vid-gen\index.html") {
    Write-Host "✅ index.html موجود" -ForegroundColor Green
    
    # فحص محتوى index.html
    $indexContent = Get-Content "dist\quran-vid-gen\index.html" -Raw
    if ($indexContent -match '<app-root>') {
        Write-Host "✅ app-root موجود في index.html" -ForegroundColor Green
    } else {
        Write-Host "❌ app-root مفقود من index.html" -ForegroundColor Red
    }
    
    if ($indexContent -match 'base href') {
        Write-Host "✅ base href موجود" -ForegroundColor Green
    } else {
        Write-Host "❌ base href مفقود" -ForegroundColor Red
    }
} else {
    Write-Host "❌ index.html مفقود" -ForegroundColor Red
}

# التحقق من ملفات JavaScript
Write-Host ""
Write-Host "2. التحقق من ملفات JavaScript..." -ForegroundColor Yellow

$jsFiles = Get-ChildItem "dist\quran-vid-gen\*.js" -ErrorAction SilentlyContinue
if ($jsFiles.Count -gt 0) {
    Write-Host "✅ تم العثور على $($jsFiles.Count) ملف JavaScript" -ForegroundColor Green
    foreach ($file in $jsFiles) {
        $size = [math]::Round($file.Length / 1KB, 2)
        Write-Host "  - $($file.Name): $size KB" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ لم يتم العثور على ملفات JavaScript" -ForegroundColor Red
}

# التحقق من ملفات CSS
Write-Host ""
Write-Host "3. التحقق من ملفات CSS..." -ForegroundColor Yellow

$cssFiles = Get-ChildItem "dist\quran-vid-gen\*.css" -ErrorAction SilentlyContinue
if ($cssFiles.Count -gt 0) {
    Write-Host "✅ تم العثور على $($cssFiles.Count) ملف CSS" -ForegroundColor Green
    foreach ($file in $cssFiles) {
        $size = [math]::Round($file.Length / 1KB, 2)
        Write-Host "  - $($file.Name): $size KB" -ForegroundColor Cyan
    }
} else {
    Write-Host "❌ لم يتم العثور على ملفات CSS" -ForegroundColor Red
}

# التحقق من تكوين Capacitor
Write-Host ""
Write-Host "4. التحقق من تكوين Capacitor..." -ForegroundColor Yellow

if (Test-Path "android\app\src\main\assets\capacitor.config.json") {
    Write-Host "✅ capacitor.config.json موجود في Android" -ForegroundColor Green
    
    $configContent = Get-Content "android\app\src\main\assets\capacitor.config.json" -Raw | ConvertFrom-Json
    Write-Host "  - App ID: $($configContent.appId)" -ForegroundColor Cyan
    Write-Host "  - Web Dir: $($configContent.webDir)" -ForegroundColor Cyan
} else {
    Write-Host "❌ capacitor.config.json مفقود من Android" -ForegroundColor Red
}

# التحقق من ملفات Android
Write-Host ""
Write-Host "5. التحقق من ملفات Android..." -ForegroundColor Yellow

if (Test-Path "android\app\src\main\assets\public") {
    $assetFiles = Get-ChildItem "android\app\src\main\assets\public" -Recurse -File
    Write-Host "✅ تم العثور على $($assetFiles.Count) ملف في assets" -ForegroundColor Green
    
    # التحقق من index.html في assets
    if (Test-Path "android\app\src\main\assets\public\index.html") {
        Write-Host "✅ index.html موجود في assets" -ForegroundColor Green
    } else {
        Write-Host "❌ index.html مفقود من assets" -ForegroundColor Red
    }
} else {
    Write-Host "❌ مجلد assets/public مفقود" -ForegroundColor Red
}

# التحقق من MainActivity
Write-Host ""
Write-Host "6. التحقق من MainActivity..." -ForegroundColor Yellow

if (Test-Path "android\app\src\main\java\qurangen\pixelpigeon\com\MainActivity.java") {
    Write-Host "✅ MainActivity.java موجود" -ForegroundColor Green
    
    $mainActivityContent = Get-Content "android\app\src\main\java\qurangen\pixelpigeon\com\MainActivity.java" -Raw
    if ($mainActivityContent -match 'BridgeActivity') {
        Write-Host "✅ MainActivity يمتد من BridgeActivity" -ForegroundColor Green
    } else {
        Write-Host "❌ MainActivity لا يمتد من BridgeActivity" -ForegroundColor Red
    }
} else {
    Write-Host "❌ MainActivity.java مفقود" -ForegroundColor Red
}

# اقتراحات الحلول
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    اقتراحات الحلول" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host ""
Write-Host "إذا كانت المشكلة مستمرة، جرب:" -ForegroundColor Yellow
Write-Host "1. استخدم build_debug.ps1 للبناء مع التشخيص" -ForegroundColor White
Write-Host "2. تحقق من logcat بعد تثبيت التطبيق:" -ForegroundColor White
Write-Host "   adb logcat | findstr 'QuranVidGen\|chromium\|WebView'" -ForegroundColor Cyan
Write-Host "3. اضغط مرتين على شاشة التطبيق لعرض معلومات التشخيص" -ForegroundColor White
Write-Host "4. تأكد من أن الجهاز متصل بالإنترنت" -ForegroundColor White
Write-Host "5. جرب إعادة تشغيل الجهاز" -ForegroundColor White

Write-Host ""
Write-Host "للحصول على مزيد من المساعدة:" -ForegroundColor Yellow
Write-Host "- تحقق من ملف README.md" -ForegroundColor White
Write-Host "- راجع ملف BUILD_INSTRUCTIONS.md" -ForegroundColor White

Write-Host ""
Read-Host "اضغط Enter للخروج..."
