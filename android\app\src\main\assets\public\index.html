<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>QuranVidGen</title>
  <base href="./">
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
  <meta name="format-detection" content="telephone=no">
  <meta name="msapplication-tap-highlight" content="no">
  <meta http-equiv="Content-Security-Policy" content="default-src * 'unsafe-inline' 'unsafe-eval' data: gap: content:">
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <!-- Loading indicator -->
  <style>
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .loading-text {
      margin-top: 20px;
      font-family: Arial, sans-serif;
      color: #666;
    }
  </style>
</head>
<body>
  <!-- Loading indicator -->
  <div id="initial-loader" class="loading-container">
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري تحميل التطبيق...</div>
  </div>

  <app-root></app-root>
  <p-toast></p-toast>

  <!-- Hide loading indicator when app loads -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      setTimeout(function() {
        const loader = document.getElementById('initial-loader');
        if (loader) {
          loader.style.display = 'none';
        }
      }, 2000);
    });

    // Load Google APIs only if not in native app
    if (typeof window !== 'undefined' && !window.Capacitor) {
      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.async = true;
      document.head.appendChild(script);
    }
  </script>
</body>
</html>
