import { Component, OnInit } from '@angular/core';
import { QuranService } from './Services/quran.service';
import { DebugService } from './debug.service';
import { catchError, of } from 'rxjs';
import { Ayah } from './Interfaces/ayah';
import { Surah } from './Interfaces/surah';
import { Capacitor } from '@capacitor/core';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = 'QuranVidGen';
  isNativeApp = false;

  constructor(
    private quranService: QuranService,
    private debugService: DebugService
  ) {
    this.isNativeApp = Capacitor.isNativePlatform();
    this.debugService.log('App Component constructor called');
  }

  ngOnInit() {
    this.debugService.log('App Component ngOnInit started');
    this.debugService.log(`Is Native App: ${this.isNativeApp}`);
    this.debugService.log(`Platform: ${Capacitor.getPlatform()}`);

    // التحقق من حالة التطبيق
    if (this.isNativeApp) {
      this.debugService.log('Running on native platform');
      // إضافة تأخير قصير للتأكد من تحميل كل شيء
      setTimeout(() => {
        this.debugService.log('Native app initialization complete');
      }, 1000);
    } else {
      this.debugService.log('Running on web platform');
    }

    // إضافة معالج للنقر المزدوج لعرض معلومات التشخيص
    document.addEventListener('dblclick', () => {
      this.debugService.showDebugInfo();
    });
  }
}
