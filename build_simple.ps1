Write-Host "========================================" -ForegroundColor Green
Write-Host "    Building APK with Debug Info" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Set environment variables
$env:GRADLE_HOME = "C:\gradle\gradle-7.6.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

Write-Host "Setting environment variables..." -ForegroundColor Yellow
Write-Host "GRADLE_HOME: $env:GRADLE_HOME" -ForegroundColor Cyan
Write-Host "JAVA_HOME: $env:JAVA_HOME" -ForegroundColor Cyan

# Clean previous build
Write-Host ""
Write-Host "Cleaning previous build..." -ForegroundColor Yellow
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist"
    Write-Host "Removed dist folder" -ForegroundColor Green
}

if (Test-Path "android\app\build") {
    Remove-Item -Recurse -Force "android\app\build"
    Write-Host "Removed android build folder" -ForegroundColor Green
}

# Build Angular with Capacitor configuration
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Building Angular with Capacitor config..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

npm run build:capacitor
if ($LASTEXITCODE -eq 0) {
    Write-Host "Angular build successful" -ForegroundColor Green
} else {
    Write-Host "Angular build failed, trying regular build..." -ForegroundColor Yellow
    npm run build
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build completely failed" -ForegroundColor Red
        Read-Host "Press Enter to exit..."
        exit 1
    }
}

# Check build files
Write-Host ""
Write-Host "Checking build files..." -ForegroundColor Yellow

$requiredFiles = @(
    "dist\quran-vid-gen\index.html",
    "dist\quran-vid-gen\main.*.js",
    "dist\quran-vid-gen\polyfills.*.js",
    "dist\quran-vid-gen\runtime.*.js"
)

$allFilesExist = $true
foreach ($pattern in $requiredFiles) {
    $files = Get-ChildItem -Path $pattern -ErrorAction SilentlyContinue
    if ($files.Count -eq 0) {
        Write-Host "Missing file: $pattern" -ForegroundColor Red
        $allFilesExist = $false
    } else {
        Write-Host "Found: $($files[0].Name)" -ForegroundColor Green
    }
}

if (-not $allFilesExist) {
    Write-Host "Some required files are missing" -ForegroundColor Red
    Read-Host "Press Enter to exit..."
    exit 1
}

# Sync Capacitor
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Syncing Capacitor..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

npx cap sync android
if ($LASTEXITCODE -eq 0) {
    Write-Host "Capacitor sync successful" -ForegroundColor Green
} else {
    Write-Host "Capacitor sync failed" -ForegroundColor Red
    Read-Host "Press Enter to exit..."
    exit 1
}

# Build APK with debug info
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Building APK with debug info..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

cd android
.\gradlew.bat assembleDebug --info --stacktrace
$buildResult = $LASTEXITCODE
cd ..

if ($buildResult -eq 0) {
    Write-Host "APK build successful" -ForegroundColor Green
} else {
    Write-Host "APK build failed, trying Capacitor run..." -ForegroundColor Yellow
    
    npx cap run android --no-open
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Build completely failed" -ForegroundColor Red
        Read-Host "Press Enter to exit..."
        exit 1
    }
}

# Find and copy APK file
Write-Host ""
Write-Host "Looking for APK file..." -ForegroundColor Yellow

$apkPaths = @(
    "android\app\build\outputs\apk\debug\app-debug.apk",
    "android\app\build\outputs\apk\release\app-release.apk"
)

$apkFound = $false
$apkPath = ""

foreach ($path in $apkPaths) {
    if (Test-Path $path) {
        $apkPath = $path
        $apkFound = $true
        Write-Host "Found APK: $path" -ForegroundColor Green
        break
    }
}

if ($apkFound) {
    # File information
    $fileInfo = Get-Item $apkPath
    $fileSize = [math]::Round($fileInfo.Length / 1MB, 2)
    Write-Host "File size: $fileSize MB" -ForegroundColor Cyan
    
    # Create filename with timestamp
    $timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm"
    $desktopPath = "$env:USERPROFILE\Desktop\QuranVidGen_Debug_$timestamp.apk"
    
    try {
        Copy-Item $apkPath $desktopPath -Force
        Write-Host "APK copied to desktop successfully" -ForegroundColor Green
        
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "APK Build Successful!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "Filename: QuranVidGen_Debug_$timestamp.apk" -ForegroundColor White
        Write-Host "File size: $fileSize MB" -ForegroundColor White
        Write-Host "File path: $desktopPath" -ForegroundColor White
        Write-Host ""
        Write-Host "Debug Instructions:" -ForegroundColor Yellow
        Write-Host "1. Install the app on your device" -ForegroundColor White
        Write-Host "2. Double-tap the screen to show debug info" -ForegroundColor White
        Write-Host "3. If white screen appears, check logcat:" -ForegroundColor White
        Write-Host "   adb logcat | findstr QuranVidGen" -ForegroundColor Cyan
        
    } catch {
        Write-Host "Error copying file: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Original APK path: $apkPath" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "APK file not found" -ForegroundColor Red
    Write-Host "Checking build folders:" -ForegroundColor Yellow
    Get-ChildItem -Path "android\app\build\outputs" -Recurse -Filter "*.apk" -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Host "  - $($_.FullName)" -ForegroundColor Cyan
    }
}

Write-Host ""
Write-Host "Process completed!" -ForegroundColor Green
Read-Host "Press Enter to exit..."
