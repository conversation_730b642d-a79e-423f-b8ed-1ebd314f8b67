Write-Host "========================================" -ForegroundColor Green
Write-Host "    Building APK - White Screen Fix" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Set environment variables
$env:GRADLE_HOME = "C:\gradle\gradle-7.6.4"
$env:PATH = "$env:GRADLE_HOME\bin;$env:PATH"
$env:JAVA_HOME = "C:\Program Files\Microsoft\jdk-*********-hotspot"

Write-Host "Setting environment variables..." -ForegroundColor Yellow

# Clean everything thoroughly
Write-Host ""
Write-Host "Cleaning all build artifacts..." -ForegroundColor Yellow

# Stop any running processes
Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
Get-Process -Name "ng" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue

# Clean directories
$dirsToClean = @("dist", "android\app\build", "android\app\src\main\assets\public", ".angular")
foreach ($dir in $dirsToClean) {
    if (Test-Path $dir) {
        try {
            Remove-Item -Recurse -Force $dir -ErrorAction Stop
            Write-Host "Cleaned: $dir" -ForegroundColor Green
        } catch {
            Write-Host "Warning: Could not clean $dir" -ForegroundColor Yellow
        }
    }
}

# Clear npm cache
Write-Host "Clearing npm cache..." -ForegroundColor Yellow
npm cache clean --force

# Install dependencies if needed
if (-not (Test-Path "node_modules")) {
    Write-Host "Installing dependencies..." -ForegroundColor Yellow
    npm install
}

# Build Angular with production configuration
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Building Angular (Production Mode)..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Try capacitor config first, then fallback to production
npm run build:capacitor
if ($LASTEXITCODE -ne 0) {
    Write-Host "Capacitor build failed, trying production build..." -ForegroundColor Yellow
    npm run build:prod
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Production build failed, trying default build..." -ForegroundColor Yellow
        npm run build
        if ($LASTEXITCODE -ne 0) {
            Write-Host "All builds failed!" -ForegroundColor Red
            Read-Host "Press Enter to exit..."
            exit 1
        }
    }
}

Write-Host "Angular build completed successfully" -ForegroundColor Green

# Verify build output
Write-Host ""
Write-Host "Verifying build output..." -ForegroundColor Yellow

$buildDir = "dist\quran-vid-gen"
if (-not (Test-Path $buildDir)) {
    Write-Host "Build directory not found!" -ForegroundColor Red
    exit 1
}

# Check critical files
$criticalFiles = @("index.html", "main.*.js", "polyfills.*.js", "runtime.*.js", "styles.*.css")
$missingFiles = @()

foreach ($pattern in $criticalFiles) {
    $files = Get-ChildItem -Path "$buildDir\$pattern" -ErrorAction SilentlyContinue
    if ($files.Count -eq 0) {
        $missingFiles += $pattern
        Write-Host "Missing: $pattern" -ForegroundColor Red
    } else {
        $file = $files[0]
        $size = [math]::Round($file.Length / 1KB, 2)
        Write-Host "Found: $($file.Name) ($size KB)" -ForegroundColor Green
    }
}

if ($missingFiles.Count -gt 0) {
    Write-Host "Critical files are missing!" -ForegroundColor Red
    exit 1
}

# Check index.html content
$indexPath = "$buildDir\index.html"
$indexContent = Get-Content $indexPath -Raw
if ($indexContent -notmatch '<app-root>') {
    Write-Host "index.html is missing app-root!" -ForegroundColor Red
    exit 1
}

Write-Host "Build verification passed" -ForegroundColor Green

# Sync with Capacitor
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Syncing with Capacitor..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

npx cap sync android --verbose
if ($LASTEXITCODE -ne 0) {
    Write-Host "Capacitor sync failed!" -ForegroundColor Red
    exit 1
}

# Verify assets were copied
$assetsPath = "android\app\src\main\assets\public"
if (-not (Test-Path "$assetsPath\index.html")) {
    Write-Host "Assets not copied properly!" -ForegroundColor Red
    exit 1
}

Write-Host "Capacitor sync completed" -ForegroundColor Green

# Build APK
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Building APK..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

cd android
.\gradlew.bat clean assembleDebug --info
$buildResult = $LASTEXITCODE
cd ..

if ($buildResult -ne 0) {
    Write-Host "APK build failed!" -ForegroundColor Red
    exit 1
}

# Find and copy APK
$apkPath = "android\app\build\outputs\apk\debug\app-debug.apk"
if (-not (Test-Path $apkPath)) {
    Write-Host "APK file not found!" -ForegroundColor Red
    exit 1
}

$fileInfo = Get-Item $apkPath
$fileSize = [math]::Round($fileInfo.Length / 1MB, 2)
$timestamp = Get-Date -Format "yyyy-MM-dd_HH-mm"
$desktopPath = "$env:USERPROFILE\Desktop\QuranVidGen_Fixed_$timestamp.apk"

Copy-Item $apkPath $desktopPath -Force

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "APK Build Successful!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "File: QuranVidGen_Fixed_$timestamp.apk" -ForegroundColor White
Write-Host "Size: $fileSize MB" -ForegroundColor White
Write-Host "Path: $desktopPath" -ForegroundColor White
Write-Host ""
Write-Host "Testing Instructions:" -ForegroundColor Yellow
Write-Host "1. Install the new APK" -ForegroundColor White
Write-Host "2. If white screen appears:" -ForegroundColor White
Write-Host "   - Double-tap for debug info" -ForegroundColor White
Write-Host "   - Triple-tap to reload" -ForegroundColor White
Write-Host "   - Check logcat: adb logcat | findstr QuranVidGen" -ForegroundColor White
Write-Host "3. The app now has better error handling and fallbacks" -ForegroundColor White

Read-Host "Press Enter to exit..."
