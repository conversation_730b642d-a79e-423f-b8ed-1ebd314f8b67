<!doctype html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="utf-8">
  <title>QuranVidGen</title>
  <base href="./">
  <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no, viewport-fit=cover">
  <meta name="format-detection" content="telephone=no">
  <meta name="msapplication-tap-highlight" content="no">
  <meta http-equiv="Content-Security-Policy" content="default-src * 'unsafe-inline' 'unsafe-eval' data: gap: content: file: blob: ws: wss:">
  <link rel="icon" type="image/x-icon" href="favicon.ico">

  <!-- Loading indicator -->
  <style>
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #3498db;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .loading-text {
      margin-top: 20px;
      font-family: Arial, sans-serif;
      color: #666;
    }
  </style>
</head>
<body>
  <!-- Loading indicator -->
  <div id="initial-loader" class="loading-container">
    <div class="loading-spinner"></div>
    <div class="loading-text">جاري تحميل التطبيق...</div>
  </div>

  <app-root>
    <!-- Fallback content if Angular fails to load -->
    <div id="fallback-content" style="display: none; padding: 20px; text-align: center; font-family: Arial, sans-serif;">
      <h2>مولد مقاطع القرآن الكريم</h2>
      <p>جاري تحميل التطبيق...</p>
      <div style="margin: 20px 0;">
        <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto;"></div>
      </div>
      <button onclick="location.reload()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 20px;">إعادة تحميل</button>
    </div>
  </app-root>
  <p-toast></p-toast>

  <!-- Enhanced loading and error handling -->
  <script>
    let appLoadTimeout;
    let loaderHidden = false;

    function hideLoader() {
      if (!loaderHidden) {
        const loader = document.getElementById('initial-loader');
        if (loader) {
          loader.style.display = 'none';
          loaderHidden = true;
        }
      }
    }

    function showFallback() {
      const fallback = document.getElementById('fallback-content');
      if (fallback) {
        fallback.style.display = 'block';
      }
      hideLoader();
    }

    // Set timeout to show fallback if app doesn't load
    appLoadTimeout = setTimeout(function() {
      console.warn('App loading timeout - showing fallback');
      showFallback();
    }, 10000); // 10 seconds timeout

    // Listen for Angular app ready
    document.addEventListener('DOMContentLoaded', function() {
      // Check if Angular components are loaded after a delay
      setTimeout(function() {
        const appRoot = document.querySelector('app-root');
        const hasAngularContent = appRoot && appRoot.children.length > 1; // More than just fallback

        if (hasAngularContent) {
          console.log('Angular app loaded successfully');
          clearTimeout(appLoadTimeout);
          hideLoader();
        } else {
          console.warn('Angular app not detected - showing fallback');
          showFallback();
        }
      }, 3000);
    });

    // Load Google APIs only if not in native app
    if (typeof window !== 'undefined' && !window.Capacitor) {
      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.async = true;
      script.onerror = function() {
        console.warn('Failed to load Google APIs');
      };
      document.head.appendChild(script);
    }

    // Additional error handling
    window.addEventListener('error', function(event) {
      console.error('Page error:', event.error);
      if (event.error && event.error.message && event.error.message.includes('Loading chunk')) {
        console.warn('Chunk loading error detected - reloading page');
        setTimeout(() => location.reload(), 2000);
      }
    });
  </script>
</body>
</html>
